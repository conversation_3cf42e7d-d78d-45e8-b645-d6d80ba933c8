"""
Writer Agent
-----------
Takes the enhanced research and writes a one-person narration story in Hindi (Devanagari).
"""

import os
import json
import logging

from langchain_openai import ChatOpenAI
from crewai import Agent, Task, Crew, Process
from crewai_tools import SerperDevTool, ScrapeWebsiteTool

from utils.parsers import StoryParser
from utils.content_moderation import sanitize_prompt
from models.schema import Story, EnhancedResearchData

logger = logging.getLogger(__name__)

class WriterAgent:
    def __init__(self, verbose: bool = False, model: str = "gpt-4o-mini", provider: str = "openai"):
        """
        Initialize the writer agent with necessary API keys and tools.

        Args:
            verbose (bool): Whether to enable verbose output from CrewAI.
                Defaults to False.
            model (str): LLM model to use. Defaults to "gpt-4o-mini".
            provider (str): LLM provider to use. Defaults to "openai".
        """
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.serper_api_key = os.getenv("SERPER_API_KEY")
        self.verbose = verbose
        self.model = model
        self.provider = provider

        if not self.openai_api_key or not self.serper_api_key:
            raise ValueError("Missing required API keys for WriterAgent")

        # Initialize tools
        self.serper_tool = SerperDevTool()
        self.scrape_website_tool = ScrapeWebsiteTool()
        self.tools = [self.serper_tool, self.scrape_website_tool]

        # Initialize the LLM
        self.llm = ChatOpenAI(
            model=self.model,
            temperature=0.8,  # Slightly higher temperature for creativity
            api_key=self.openai_api_key
        )

    def write_story(self,
                    enhanced_research: EnhancedResearchData,
                    title: str,
                    story_type: str = 'real',
                    genre: str = 'thriller') -> Story:
        """
        Write a Hindi story using the enhanced research.

        Args:
            enhanced_research (EnhancedResearchData): The enhanced research data with narrative elements
            title (str): The title of the story
            story_type (str): Type of story - 'real', 'fictional', or 'mixed'
            genre (str): Genre of the story - thriller, romance, mystery, etc.

        Returns:
            Story: Story data in structured format using Pydantic model
        """
        logger.info(f"Starting {genre} story writing for title: '{title}'")

        # Create a parser for the Story model
        parser = StoryParser.create()
        format_instructions = parser.get_format_instructions()

        # Create the writer agent with role based on story type and genre
        if story_type == 'real':
            writer_role = f"Hindi {genre.capitalize()} Writer for Real Incidents"
            writer_goal = f"Write a compelling Hindi documentary-style narration inspired by Nitish Rajput"
            writer_backstory = f"""You are a professional Hindi storyteller and documentary narrator specializing in factual narratives.
            You create engaging, informative content that presents real events with journalistic integrity and compelling storytelling techniques.
            Your narration style is clear, engaging, and educational, focusing on presenting facts in an accessible and interesting manner.
            You emphasize human interest elements, social context, and factual accuracy while maintaining audience engagement."""

        elif story_type == 'fictional':
            writer_role = f"Hindi {genre.capitalize()} Fiction Writer"
            writer_goal = f"Write a compelling fictional Hindi {genre} story for narration"
            writer_backstory = f"""You are a professional Hindi creative writer specializing in {genre} storytelling.
            You create engaging fictional narratives that focus on character development, plot progression, and emotional resonance.
            Your writing emphasizes positive themes, human connections, and meaningful storytelling while maintaining appropriate content standards."""

        else:  # story_type == 'mixed'
            writer_role = f"Hindi {genre.capitalize()} Hybrid Writer"
            writer_goal = f"Write a compelling Hindi {genre} story blending real incidents with fictional elements"
            writer_backstory = f"""You are a professional Hindi writer specializing in narrative storytelling that combines factual elements with creative fiction.
            You create balanced narratives that respect factual accuracy while enhancing engagement through appropriate creative elements.
            Your writing maintains journalistic integrity while using storytelling techniques to create compelling, educational content."""

        writer = Agent(
            role=writer_role,
            goal=writer_goal,
            backstory=writer_backstory,
            verbose=self.verbose,
            allow_delegation=False,
            tools=self.tools,
            llm=self.llm
        )

        # Convert enhanced research to string for the task
        research_str = json.dumps(enhanced_research.model_dump(), ensure_ascii=False, indent=2)

        # Create the writing task with description based on story type and genre
        if story_type == 'real':
            task_description = f"""
            Write a well-structured Hindi (Devanagari) story script based on the enhanced research below. The story should feel like a person is telling a true story to the audience — serious, engaging, and layered with emotional tension, context, and closure.

            {research_str}

            The title should be: "{title}"

            Requirements:
            1. The story must be fully narrated in Hindi (Devanagari script)
            2. Narration should be from a single storyteller's voice — first-person or neutral, not screenplay style
            3. Use rhetorical questions, dramatic pauses, and callbacks for effect
            4. Blend factual information with emotional storytelling
            5. Open with a strong hook line
            6. Write approximately 10–12 scenes, structured with clear narrative flow and visual descriptions
            7. Each scene should have:
               - narration (in Hindi)
               - visual_description (in English, cinematic and detailed)
               - transition_from_previous (in English)
               - narrative_purpose (hook, rising tension, climax, resolution, etc.)

            Additional Guidelines:
            - Avoid bullet points or mechanical tone
            - Include sensory details: sound, light, expression, environment
            - Embed emotional stakes in every scene
            - Use dramatic yet authentic Hindi — poetic in parts, precise when conveying facts
            - Emphasize character-driven storytelling (not just event-driven)
            - Use cultural symbols, Indian ethos, and grounded realism
            - End with a reflective closing or takeaway line that provokes thought

            IMPORTANT:
            - This is a narrated story, not a screenplay.
            - Dialogue should be minimal and embedded within narration naturally.
            - Output must be returned in structured JSON with fields: `scene_number`, `narration`, `visual_description`, `transition_from_previous`, `narrative_purpose`
            - ALL narration text MUST be in Hindi using Devanagari script - this is the content that will be spoken in the final video
            - Visual descriptions, transitions, and narrative purpose should be in English
            - Create seamless transitions between scenes to maintain listener engagement
            - Maintain proper pacing that builds tension and keeps listeners engaged
            - Use the search and web scraping tools to research additional details that can enhance authenticity
            - After writing, review the story to ensure it is easily understandable and follows a logical progression
            """

        elif story_type == 'fictional':
            task_description = f"""
            Write a compelling fictional Hindi {genre} story using the following enhanced creative elements:

            {research_str}

            The title should be: "{title}"

            Pay special attention to:
            1. The narrative structure provided in the enhanced research
            2. The thematic elements identified
            3. The emotional arcs for character development
            4. The dialogue suggestions for authentic conversations
            5. The cultural authenticity notes to ensure proper representation
            6. The conventions and tropes of the {genre} genre

            Your task is to:

            1. Write a 10-minute narration story in Hindi (Devanagari script) in the {genre} genre - ALL narration MUST be in Hindi using Devanagari script
            2. Structure the story into scenes (approximately 8-12 scenes) following a cohesive narrative arc
            3. Create a powerful opening hook that immediately grabs the listener's attention
            4. Each scene should have a clear narrative and vivid visual description
            5. Include atmospheric elements to create an engaging and immersive narrative appropriate for a {genre} story
            6. Incorporate the thematic elements and emotional arcs throughout the story
            7. Ensure the story has a strong beginning, compelling middle, and satisfying conclusion
            8. Be creative and imaginative while respecting the cultural context
            9. Use the conventions of the {genre} genre to enhance the storytelling
            10. Create seamless transitions between scenes to maintain listener engagement
            11. Develop clear character motivations that drive the narrative forward
            12. Use natural-sounding dialogue that feels authentic and reveals character
            13. Maintain proper pacing that builds tension and keeps listeners engaged
            14. Use the search and web scraping tools to research similar stories and tropes that can enhance your creativity
            15. After writing, review the story to ensure it is easily understandable and follows a logical progression

            IMPORTANT NARRATIVE QUALITY GUIDELINES:
            1. ALL narration text MUST be in Hindi using Devanagari script - this is the content that will be spoken in the final video
            2. Visual descriptions, transitions, and narrative purpose should be in English
            3. For each scene, include a clear purpose in the narrative (e.g., hook, character introduction, plot development, climax, resolution)
            4. Create explicit transitions between scenes that connect the narrative flow
            5. Vary sentence structure and length to create rhythm and emotional impact
            6. Use sensory details to make the story more immersive
            7. Balance narration, description, and dialogue for better pacing
            8. Create emotional stakes that make the audience care about the outcome
            9. Use foreshadowing and callbacks to create a cohesive narrative
            10. Ensure each scene advances the plot or develops characters in meaningful ways
            11. Create moments of tension and release throughout the story
            12. End each scene with a mini-hook that leads naturally to the next scene
            """

        else:  # story_type == 'mixed'
            task_description = f"""
            Write a compelling Hindi {genre} story blending real incidents with fictional elements using the following enhanced research:

            {research_str}

            The title should be: "{title}"

            Pay special attention to:
            1. The narrative structure provided in the enhanced research
            2. The thematic elements identified
            3. The emotional arcs for character development
            4. The dialogue suggestions for authentic conversations
            5. The cultural authenticity notes to ensure proper representation
            6. The conventions and tropes of the {genre} genre
            7. The balance between factual elements and fictional enhancements

            Your task is to:

            1. Write a 10-minute narration story in Hindi (Devanagari script) in the {genre} genre - ALL narration MUST be in Hindi using Devanagari script
            2. Structure the story into scenes (approximately 8-12 scenes) following a cohesive narrative arc
            3. Create a powerful opening hook that immediately grabs the listener's attention
            4. Each scene should have a clear narrative and vivid visual description
            5. Include atmospheric elements to create an engaging narrative appropriate for a {genre} story
            6. Incorporate the thematic elements and emotional arcs throughout the story
            7. Ensure the story has a strong beginning, compelling middle, and satisfying conclusion
            8. Balance factual accuracy with creative storytelling
            9. Use the conventions of the {genre} genre to enhance the storytelling
            10. Create seamless transitions between scenes to maintain listener engagement
            11. Develop clear character motivations that drive the narrative forward
            12. Use natural-sounding dialogue that feels authentic and reveals character
            13. Maintain proper pacing that builds tension and keeps listeners engaged
            14. Use the search and web scraping tools to research additional details that can enhance both authenticity and creativity
            15. After writing, review the story to ensure it is easily understandable and follows a logical progression

            IMPORTANT NARRATIVE QUALITY GUIDELINES:
            1. ALL narration text MUST be in Hindi using Devanagari script - this is the content that will be spoken in the final video
            2. Visual descriptions, transitions, and narrative purpose should be in English
            3. For each scene, include a clear purpose in the narrative (e.g., hook, character introduction, plot development, climax, resolution)
            4. Create explicit transitions between scenes that connect the narrative flow
            5. Vary sentence structure and length to create rhythm and emotional impact
            6. Use sensory details to make the story more immersive
            7. Balance narration, description, and dialogue for better pacing
            8. Create emotional stakes that make the audience care about the outcome
            9. Use foreshadowing and callbacks to create a cohesive narrative
            10. Ensure each scene advances the plot or develops characters in meaningful ways
            11. Create moments of tension and release throughout the story
            12. End each scene with a mini-hook that leads naturally to the next scene
            """

        # Create the writing task
        writing_task = Task(
            description=task_description,
            agent=writer,
            expected_output=format_instructions,
            llm=self.llm
        )

        # Create and run the crew
        crew = Crew(
            process=Process.sequential,
            tasks=[writing_task],
            agents=[writer],
            manager_llm=self.llm,
            verbose=self.verbose,
        )

        crew_output = crew.kickoff()
        result = crew_output.raw

        # Apply content moderation to the raw result before parsing
        sanitized_result = sanitize_prompt(result)

        # Parse the result using the Pydantic parser
        story = StoryParser.parse_output(parser, sanitized_result)

        # If parsing fails, create a basic structure
        if story is None:
            logger.warning("Could not parse Writer result, Raw output: %s", result)
            logger.warning("Please retry the task. Exiting...")
            exit(1)

        logger.info(f"Story writing completed successfully with {len(story.scenes)} scenes")
        return story
