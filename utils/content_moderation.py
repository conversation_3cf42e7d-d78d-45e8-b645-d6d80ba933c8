"""
Content Moderation Utility
--------------------------
Sanitizes image generation prompts to avoid triggering BFL's content moderation filters
while maintaining narrative clarity and visual accuracy.
"""

import re
import logging
from typing import List, Tuple

logger = logging.getLogger(__name__)


class ContentModerator:
    """
    Content moderation utility for sanitizing image generation prompts.
    
    This class implements content filtering to avoid triggering BFL's content moderation
    while preserving the narrative intent and visual accuracy of the prompts.
    """
    
    def __init__(self):
        """Initialize the content moderator with prohibited terms and their replacements."""
        
        # Dictionary mapping prohibited terms to safe alternatives
        self.term_replacements = {
            # Violence and conflict terms
            "terrorist": "hostile figure",
            "terrorists": "hostile figures",
            "terrorism": "hostile activities",
            "jihad": "extremist ideology",
            "jihadist": "extremist",
            "jihadists": "extremists",
            "radicalized": "influenced by extremist views",
            "radicalizing": "influencing with extremist views",
            "militant": "activist",
            "militants": "activists",
            "insurgent": "rebel figure",
            "insurgents": "rebel figures",
            "extremist": "radical figure",
            "extremists": "radical figures",

            # Physical harm terms
            "blood": "red stains",
            "bloody": "stained with red",
            "bleeding": "showing red stains",
            "wound": "visible injury",
            "wounded": "visibly injured",
            "wounds": "visible injuries",
            "injury": "physical distress",
            "injured": "under physical pressure",
            "bruise": "mark",
            "bruised": "marked",
            "bruises": "marks",
            "scar": "mark",
            "scarred": "marked",
            "scars": "marks",
            
            # Violence action terms
            "kill": "neutralize",
            "killing": "neutralizing",
            "killed": "neutralized",
            "murder": "elimination",
            "murdered": "eliminated",
            "murdering": "eliminating",
            "assassinate": "eliminate target",
            "assassinated": "eliminated target",
            "assassinating": "eliminating target",
            
            # Weapons and combat terms
            "gun": "tactical equipment",
            "guns": "tactical equipment",
            "weapon": "professional equipment",
            "weapons": "professional equipment",
            "rifle": "tactical gear",
            "pistol": "sidearm equipment",
            "firearm": "tactical device",
            "firearms": "tactical devices",
            "gunshot": "projectile impact",
            "gunshots": "projectile impacts",
            "shooting": "tactical posture",
            "shoot": "take tactical position",
            "shot": "projectile event",
            "gunfire": "ballistic activity",
            "gunfight": "tactical confrontation",
            "firefight": "engagement scene",
            
            # Death and casualties
            "dead": "fallen",
            "death": "casualties",
            "deaths": "casualties",
            "died": "fell",
            "dying": "falling",
            "corpse": "fallen figure",
            "corpses": "fallen figures",
            "body": "figure",
            "bodies": "figures",
            
            # Explosives and destruction
            "bomb": "explosive device",
            "bombs": "explosive devices",
            "bombing": "detonation event",
            "bombed": "affected by detonation",
            "explosion": "detonation event",
            "explosions": "detonation events",
            "explode": "detonate",
            "exploded": "detonated",
            "exploding": "detonating",
            "blast": "detonation incident",
            "blasts": "detonation incidents",
            
            # General violence terms
            "violence": "intense confrontation",
            "violent": "intensely confrontational",
            "attack": "engagement",
            "attacked": "engaged",
            "attacking": "engaging",
            "assault": "confrontation",
            "assaulted": "confronted",
            "assaulting": "confronting",
            
            # Military and operations terms
            "anti-terror operation": "high-risk mission",
            "counter-terrorism": "security operation",
            "military operation": "strategic mission",
            "combat": "tactical engagement",
            "battle": "strategic confrontation",
            "war": "conflict situation",
            "warfare": "strategic conflict",

            # Additional problematic terms
            "knife": "metallic tool",
            "blade": "sharp implement",
            "sword": "metallic implement",
            "ammunition": "tactical supplies",
            "bullets": "projectile components",
            "grenade": "tactical device",
            "explosive": "detonation device",
            "hostage": "person under duress",
            "kidnap": "unlawful detention",
            "torture": "intense pressure",
            "execution": "elimination procedure",

            # Emotional and psychological terms
            "fear": "concern",
            "terror": "intense concern",
            "panic": "urgency",
            "horror": "distress",
            "nightmare": "difficult situation",
            "trauma": "challenging experience",
            "traumatic": "challenging",
            "disturbing": "concerning",
            "shocking": "surprising",
            "brutal": "intense",
            "savage": "intense",
            "ruthless": "determined",
            "merciless": "unwavering",

            # Political and ideological terms
            "radical": "alternative",
            "fundamentalist": "traditional figure",
            "sectarian": "community-based",
            "ethnic cleansing": "community displacement",
            "genocide": "mass displacement",
            "persecution": "systematic pressure",
            "oppression": "restrictive conditions",
            "regime": "administration",
            "dictatorship": "authoritarian system",
            "coup": "political transition",

            # Crime and law enforcement
            "criminal": "individual",
            "crime": "incident",
            "illegal": "unauthorized",
            "arrest": "detention",
            "arrested": "detained",
            "prison": "detention facility",
            "jail": "holding facility",
            "convict": "individual",
            "guilty": "responsible",
            "suspect": "person of interest",

            # Medical and health terms that might be sensitive
            "disease": "health condition",
            "epidemic": "health situation",
            "pandemic": "widespread health situation",
            "virus": "pathogen",
            "infection": "health issue",
            "contamination": "exposure situation",
            "quarantine": "isolation period",

            # Additional violence-related terms
            "fight": "confrontation",
            "fighting": "confronting",
            "punch": "physical contact",
            "kick": "physical movement",
            "hit": "contact",
            "strike": "contact",
            "slap": "contact gesture",
            "choke": "restrict breathing",
            "strangle": "restrict breathing",
            "suffocate": "breathing difficulty"
        }
        
        # Compile regex patterns for efficient replacement
        self.replacement_patterns = self._compile_replacement_patterns()
        
    def _compile_replacement_patterns(self) -> List[Tuple[re.Pattern, str]]:
        """
        Compile regex patterns for term replacement.
        
        Returns:
            List[Tuple[re.Pattern, str]]: List of compiled regex patterns and their replacements
        """
        patterns = []
        
        for prohibited_term, replacement in self.term_replacements.items():
            # Create case-insensitive pattern with word boundaries
            pattern = re.compile(r'\b' + re.escape(prohibited_term) + r'\b', re.IGNORECASE)
            patterns.append((pattern, replacement))
            
        return patterns
    
    def sanitize_prompt(self, prompt: str) -> str:
        """
        Sanitize an image generation prompt by replacing prohibited terms.
        
        Args:
            prompt (str): The original prompt text
            
        Returns:
            str: The sanitized prompt with prohibited terms replaced
        """
        if not prompt or not isinstance(prompt, str):
            return prompt
            
        sanitized = prompt
        replacements_made = []
        
        # Apply all term replacements
        for pattern, replacement in self.replacement_patterns:
            matches = pattern.findall(sanitized)
            if matches:
                replacements_made.extend([(match, replacement) for match in matches])
                sanitized = pattern.sub(replacement, sanitized)
        
        # Log replacements if any were made
        if replacements_made:
            logger.info(f"Content moderation applied: {len(replacements_made)} terms replaced")
            for original, replacement in replacements_made:
                logger.debug(f"Replaced '{original}' with '{replacement}'")
        
        return sanitized
    
    def sanitize_character_description(self, description: str) -> str:
        """
        Sanitize character descriptions for consistency prompts.
        
        Args:
            description (str): The original character description
            
        Returns:
            str: The sanitized character description
        """
        return self.sanitize_prompt(description)
    
    def sanitize_scene_description(self, description: str) -> str:
        """
        Sanitize scene descriptions for image prompts.
        
        Args:
            description (str): The original scene description
            
        Returns:
            str: The sanitized scene description
        """
        return self.sanitize_prompt(description)
    
    def is_content_safe(self, text: str) -> bool:
        """
        Check if content contains any prohibited terms.
        
        Args:
            text (str): The text to check
            
        Returns:
            bool: True if content is safe, False if it contains prohibited terms
        """
        if not text or not isinstance(text, str):
            return True
            
        for pattern, _ in self.replacement_patterns:
            if pattern.search(text):
                return False
                
        return True
    
    def get_prohibited_terms_found(self, text: str) -> List[str]:
        """
        Get a list of prohibited terms found in the text.
        
        Args:
            text (str): The text to analyze
            
        Returns:
            List[str]: List of prohibited terms found
        """
        if not text or not isinstance(text, str):
            return []
            
        found_terms = []
        for pattern, _ in self.replacement_patterns:
            matches = pattern.findall(text)
            found_terms.extend(matches)
            
        return found_terms


# Global instance for easy access
content_moderator = ContentModerator()


def sanitize_prompt(prompt: str) -> str:
    """
    Convenience function to sanitize a prompt using the global moderator instance.
    
    Args:
        prompt (str): The prompt to sanitize
        
    Returns:
        str: The sanitized prompt
    """
    return content_moderator.sanitize_prompt(prompt)


def sanitize_character_description(description: str) -> str:
    """
    Convenience function to sanitize character descriptions.
    
    Args:
        description (str): The character description to sanitize
        
    Returns:
        str: The sanitized description
    """
    return content_moderator.sanitize_character_description(description)


def sanitize_scene_description(description: str) -> str:
    """
    Convenience function to sanitize scene descriptions.
    
    Args:
        description (str): The scene description to sanitize
        
    Returns:
        str: The sanitized description
    """
    return content_moderator.sanitize_scene_description(description)


def is_content_safe(text: str) -> bool:
    """
    Convenience function to check if content is safe.
    
    Args:
        text (str): The text to check
        
    Returns:
        bool: True if content is safe
    """
    return content_moderator.is_content_safe(text)
