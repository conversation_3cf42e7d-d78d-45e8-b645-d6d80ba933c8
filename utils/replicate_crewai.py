"""
Replicate CrewAI Adapter
------------------------
Adapter that allows using Replicate's <PERSON><PERSON><PERSON><PERSON> integration with CrewAI.
"""

import os
import logging
from typing import Optional
from crewai import BaseLLM
from utils.replicate_llm import ChatReplicate

logger = logging.getLogger(__name__)


class ChatReplicateCrewAI(BaseLLM):
    """
    A wrapper around LangChain's ChatReplicate for CrewAI that implements the CrewAI BaseLLM interface.
    """
    
    def __init__(
        self,
        model: str = "meta/llama-2-70b-chat",
        temperature: float = 0.7,
        max_tokens: int = 1000,
        top_p: float = 1.0,
        api_token: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize the Replicate LLM for CrewAI.
        
        Args:
            model (str): The Replicate model to use. Default: "meta/llama-2-70b-chat"
            temperature (float): The temperature for generation. Default: 0.7
            max_tokens (int): Maximum tokens to generate. Default: 1000
            top_p (float): Top-p sampling parameter. Default: 1.0
            api_token (str, optional): The Replicate API token. If not provided, will use REPLICATE_API_KEY env var.
            **kwargs: Additional arguments to pass to the LLM.
        """
        super().__init__(model, temperature)
        
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.top_p = top_p
        
        api_token = api_token or os.getenv("REPLICATE_API_KEY")
        
        # Validate API token
        if not api_token:
            raise ValueError("Replicate API token is required. Set it via the api_token parameter or REPLICATE_API_KEY environment variable.")
        
        # Initialize the ChatReplicate
        self.chat_model = ChatReplicate(
            model_name=model,
            api_token=api_token,
            temperature=temperature,
            max_tokens=max_tokens,
            top_p=top_p,
            **kwargs
        )
        
        logger.info(f"Initialized ChatReplicateCrewAI with model {model}")
    
    def call(self, messages, tools=None, callbacks=None, available_functions=None):
        """
        Call the LLM with the given messages.
        
        Args:
            messages: Input messages for the LLM
            tools: Optional list of tool schemas for function calling
            callbacks: Optional list of callback functions
            available_functions: Optional dict mapping function names to callables
            
        Returns:
            Either a text response from the LLM or the result of a tool function call
        """
        # Convert string message to proper format if needed
        if isinstance(messages, str):
            messages = [{"role": "user", "content": messages}]
        
        # Convert to LangChain message format
        from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
        
        lc_messages = []
        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            
            if role == "system":
                lc_messages.append(SystemMessage(content=content))
            elif role == "user":
                lc_messages.append(HumanMessage(content=content))
            elif role == "assistant":
                lc_messages.append(AIMessage(content=content))
        
        # Call the LangChain model
        response = self.chat_model.invoke(lc_messages)
        
        # Return the content of the response
        return response.content
    
    def supports_function_calling(self) -> bool:
        """Check if the LLM supports function calling."""
        # Most Replicate models don't support function calling yet
        return False
