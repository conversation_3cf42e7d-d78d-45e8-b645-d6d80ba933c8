"""
Image Composition Utility
------------------------
Utilities for composing character reference images with labels using PIL.
"""

import os
import logging
from typing import List, Tuple, Optional
from PIL import Image, ImageDraw, ImageFont

logger = logging.getLogger(__name__)


class ImageComposer:
    """
    Utility class for composing character reference images with labels.
    
    This class provides functionality to add text labels to individual character images
    and compose multiple character images into a single group reference image.
    """
    
    def __init__(self):
        """Initialize the image composer."""
        self.default_font_size = 48
        self.label_padding = 20
        self.background_color = (255, 255, 255)  # White background
        self.text_color = (0, 0, 0)  # Black text
        
    def add_label_to_image(self, 
                          image_path: str, 
                          character_name: str, 
                          output_path: str) -> bool:
        """
        Add a character name label to an image.
        
        Args:
            image_path (str): Path to the input image
            character_name (str): Name to label the character with
            output_path (str): Path to save the labeled image
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Open the image
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Create a copy to work with
                labeled_img = img.copy()
                draw = ImageDraw.Draw(labeled_img)
                
                # Try to load a font, fall back to default if not available
                try:
                    font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", self.default_font_size)
                except (OSError, IOError):
                    try:
                        font = ImageFont.truetype("arial.ttf", self.default_font_size)
                    except (OSError, IOError):
                        font = ImageFont.load_default()
                        logger.warning("Using default font for character labels")
                
                # Get image dimensions
                img_width, img_height = labeled_img.size
                
                # Get text dimensions
                bbox = draw.textbbox((0, 0), character_name, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
                
                # Calculate position for centered text at bottom
                text_x = (img_width - text_width) // 2
                text_y = img_height - text_height - self.label_padding
                
                # Create a background rectangle for the text
                rect_padding = 10
                rect_x1 = text_x - rect_padding
                rect_y1 = text_y - rect_padding
                rect_x2 = text_x + text_width + rect_padding
                rect_y2 = text_y + text_height + rect_padding
                
                # Draw background rectangle
                draw.rectangle([rect_x1, rect_y1, rect_x2, rect_y2], 
                             fill=self.background_color, 
                             outline=self.text_color, 
                             width=2)
                
                # Draw the text
                draw.text((text_x, text_y), character_name, 
                         fill=self.text_color, font=font)
                
                # Save the labeled image
                labeled_img.save(output_path, 'PNG', quality=95)
                logger.info(f"Added label '{character_name}' to image: {output_path}")
                return True
                
        except Exception as e:
            logger.error(f"Error adding label to image {image_path}: {str(e)}")
            return False
    
    def compose_group_image(self, 
                           labeled_image_paths: List[str], 
                           output_path: str,
                           layout: str = 'horizontal') -> bool:
        """
        Compose multiple labeled character images into a single group reference image.
        
        Args:
            labeled_image_paths (List[str]): List of paths to labeled character images
            output_path (str): Path to save the composed group image
            layout (str): Layout type - 'horizontal', 'vertical', or 'grid'
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not labeled_image_paths:
            logger.warning("No labeled images provided for group composition")
            return False
            
        try:
            # Load all images
            images = []
            for img_path in labeled_image_paths:
                try:
                    with Image.open(img_path) as img:
                        images.append(img.convert('RGB').copy())
                except Exception as e:
                    logger.error(f"Error loading image {img_path}: {str(e)}")
                    continue
            
            if not images:
                logger.error("No valid images loaded for group composition")
                return False
            
            # Calculate dimensions based on layout
            if layout == 'horizontal':
                group_img = self._compose_horizontal(images)
            elif layout == 'vertical':
                group_img = self._compose_vertical(images)
            elif layout == 'grid':
                group_img = self._compose_grid(images)
            else:
                logger.error(f"Unsupported layout: {layout}")
                return False
            
            # Save the composed image
            group_img.save(output_path, 'PNG', quality=95)
            logger.info(f"Composed group image saved to: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error composing group image: {str(e)}")
            return False
    
    def _compose_horizontal(self, images: List[Image.Image]) -> Image.Image:
        """Compose images horizontally."""
        # Find the maximum height and total width
        max_height = max(img.height for img in images)
        total_width = sum(img.width for img in images)
        
        # Add spacing between images
        spacing = 20
        total_width += spacing * (len(images) - 1)
        
        # Create the group image with landscape aspect ratio (16:9)
        target_width = 1920
        target_height = 1080
        
        # Scale if necessary to fit target dimensions
        if total_width > target_width or max_height > target_height:
            scale_factor = min(target_width / total_width, target_height / max_height)
            images = [img.resize((int(img.width * scale_factor), int(img.height * scale_factor)), 
                               Image.Resampling.LANCZOS) for img in images]
            max_height = max(img.height for img in images)
            total_width = sum(img.width for img in images) + spacing * (len(images) - 1)
        
        # Create the final image
        group_img = Image.new('RGB', (target_width, target_height), self.background_color)
        
        # Calculate starting position to center the composition
        start_x = (target_width - total_width) // 2
        start_y = (target_height - max_height) // 2
        
        # Paste images
        current_x = start_x
        for img in images:
            y_offset = start_y + (max_height - img.height) // 2
            group_img.paste(img, (current_x, y_offset))
            current_x += img.width + spacing
        
        return group_img
    
    def _compose_vertical(self, images: List[Image.Image]) -> Image.Image:
        """Compose images vertically."""
        # Find the maximum width and total height
        max_width = max(img.width for img in images)
        total_height = sum(img.height for img in images)
        
        # Add spacing between images
        spacing = 20
        total_height += spacing * (len(images) - 1)
        
        # Create the group image with landscape aspect ratio (16:9)
        target_width = 1920
        target_height = 1080
        
        # Scale if necessary to fit target dimensions
        if max_width > target_width or total_height > target_height:
            scale_factor = min(target_width / max_width, target_height / total_height)
            images = [img.resize((int(img.width * scale_factor), int(img.height * scale_factor)), 
                               Image.Resampling.LANCZOS) for img in images]
            max_width = max(img.width for img in images)
            total_height = sum(img.height for img in images) + spacing * (len(images) - 1)
        
        # Create the final image
        group_img = Image.new('RGB', (target_width, target_height), self.background_color)
        
        # Calculate starting position to center the composition
        start_x = (target_width - max_width) // 2
        start_y = (target_height - total_height) // 2
        
        # Paste images
        current_y = start_y
        for img in images:
            x_offset = start_x + (max_width - img.width) // 2
            group_img.paste(img, (x_offset, current_y))
            current_y += img.height + spacing
        
        return group_img
    
    def _compose_grid(self, images: List[Image.Image]) -> Image.Image:
        """Compose images in a grid layout."""
        num_images = len(images)
        
        # Calculate grid dimensions
        if num_images <= 2:
            cols = num_images
            rows = 1
        elif num_images <= 4:
            cols = 2
            rows = 2
        elif num_images <= 6:
            cols = 3
            rows = 2
        else:
            cols = 3
            rows = (num_images + cols - 1) // cols  # Ceiling division
        
        # Find the maximum dimensions
        max_width = max(img.width for img in images)
        max_height = max(img.height for img in images)
        
        # Calculate spacing
        spacing = 20
        
        # Calculate total dimensions
        total_width = cols * max_width + (cols - 1) * spacing
        total_height = rows * max_height + (rows - 1) * spacing
        
        # Target dimensions (landscape 16:9)
        target_width = 1920
        target_height = 1080
        
        # Scale if necessary
        if total_width > target_width or total_height > target_height:
            scale_factor = min(target_width / total_width, target_height / total_height)
            images = [img.resize((int(img.width * scale_factor), int(img.height * scale_factor)), 
                               Image.Resampling.LANCZOS) for img in images]
            max_width = max(img.width for img in images)
            max_height = max(img.height for img in images)
            total_width = cols * max_width + (cols - 1) * spacing
            total_height = rows * max_height + (rows - 1) * spacing
        
        # Create the final image
        group_img = Image.new('RGB', (target_width, target_height), self.background_color)
        
        # Calculate starting position to center the grid
        start_x = (target_width - total_width) // 2
        start_y = (target_height - total_height) // 2
        
        # Paste images in grid
        for i, img in enumerate(images):
            row = i // cols
            col = i % cols
            
            x = start_x + col * (max_width + spacing)
            y = start_y + row * (max_height + spacing)
            
            # Center the image in its cell
            x_offset = x + (max_width - img.width) // 2
            y_offset = y + (max_height - img.height) // 2
            
            group_img.paste(img, (x_offset, y_offset))
        
        return group_img


# Global instance for easy access
image_composer = ImageComposer()


def add_label_to_image(image_path: str, character_name: str, output_path: str) -> bool:
    """
    Convenience function to add a label to an image.
    
    Args:
        image_path (str): Path to the input image
        character_name (str): Name to label the character with
        output_path (str): Path to save the labeled image
        
    Returns:
        bool: True if successful, False otherwise
    """
    return image_composer.add_label_to_image(image_path, character_name, output_path)


def compose_group_image(labeled_image_paths: List[str], 
                       output_path: str,
                       layout: str = 'horizontal') -> bool:
    """
    Convenience function to compose a group image.
    
    Args:
        labeled_image_paths (List[str]): List of paths to labeled character images
        output_path (str): Path to save the composed group image
        layout (str): Layout type - 'horizontal', 'vertical', or 'grid'
        
    Returns:
        bool: True if successful, False otherwise
    """
    return image_composer.compose_group_image(labeled_image_paths, output_path, layout)
