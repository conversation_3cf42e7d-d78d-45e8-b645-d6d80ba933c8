"""
Agent Factory
------------
Utility functions for creating agents with different LLM providers.
Supports OpenAI (with rate limiting) and OpenRouter.
"""

import os
import logging
from typing import List, Optional, Any

from crewai import Agent
from utils.rate_limited_llm import RateLimitedLLM
from utils.openrouter_crewai import ChatOpenRouterCrewAI
from utils.replicate_crewai import ChatReplicateCrewAI

logger = logging.getLogger(__name__)


def create_rate_limited_agent(
    role: str,
    goal: str,
    backstory: str,
    model: str = "gpt-4o-mini",
    temperature: float = 0.7,
    max_tokens_per_minute: int = 30000,
    verbose: bool = False,
    allow_delegation: bool = False,
    tools: Optional[List[Any]] = None,
    provider: str = "openai",
    **kwargs
) -> Agent:
    """
    Create a CrewAI agent with an LLM from the specified provider.

    Args:
        role (str): The role of the agent
        goal (str): The goal of the agent
        backstory (str): The backstory of the agent
        model (str): The model to use. Defaults to "gpt-4o-mini".
        temperature (float): The temperature for generation. Defaults to 0.7.
        max_tokens_per_minute (int): Maximum tokens per minute to use (OpenAI only). Defaults to 30000.
        verbose (bool): Whether to enable verbose output from CrewAI. Defaults to False.
        allow_delegation (bool): Whether to allow delegation. Defaults to False.
        tools (List[Any], optional): List of tools for the agent to use.
        provider (str): The LLM provider to use. Either "openai", "openrouter", or "replicate". Defaults to "openai".
        **kwargs: Additional arguments to pass to the Agent constructor.

    Returns:
        Agent: A CrewAI agent with the specified LLM
    """
    # Initialize the appropriate LLM based on the provider
    if provider.lower() == "openai":
        llm = RateLimitedLLM(
            model=model,
            temperature=temperature,
            max_tokens_per_minute=max_tokens_per_minute,
            api_key=os.getenv("OPENAI_API_KEY")
        )
        logger.info(f"Creating agent '{role}' with OpenAI LLM (model: {model}, max_tokens_per_minute: {max_tokens_per_minute})")

    elif provider.lower() == "openrouter":
        llm = ChatOpenRouterCrewAI(
            model=model,
            temperature=temperature,
            api_key=os.getenv("OPENROUTER_API_KEY")
        )
        logger.info(f"Creating agent '{role}' with OpenRouter LLM (model: {model})")

    elif provider.lower() == "replicate":
        llm = ChatReplicateCrewAI(
            model=model,
            temperature=temperature,
            api_token=os.getenv("REPLICATE_API_KEY")
        )
        logger.info(f"Creating agent '{role}' with Replicate LLM (model: {model})")

    else:
        raise ValueError(f"Unsupported provider: {provider}. Use 'openai', 'openrouter', or 'replicate'.")

    # Create and return the agent
    return Agent(
        role=role,
        goal=goal,
        backstory=backstory,
        verbose=verbose,
        allow_delegation=allow_delegation,
        tools=tools or [],
        llm=llm,
        **kwargs
    )
